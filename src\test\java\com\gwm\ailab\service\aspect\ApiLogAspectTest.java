package com.gwm.ailab.service.aspect;

import com.alibaba.fastjson2.JSONObject;
import com.gwm.ailab.service.controller.TemplateController;
import com.gwm.ailab.service.service.CardTemplateService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * API日志切面测试类
 * 验证AOP日志记录功能，特别是入参信息的记录
 * 
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@WebMvcTest(TemplateController.class)
class ApiLogAspectTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CardTemplateService cardTemplateService;

    /**
     * 测试模板同步接口的日志记录
     * 验证入参信息是否正确记录
     */
    @Test
    void testTemplateSyncLogging() throws Exception {
        // 准备测试数据
        JSONObject requestJson = new JSONObject();
        requestJson.put("code", "TEST_TEMPLATE_001");
        requestJson.put("content", "{\"title\":\"测试模板\",\"description\":\"这是一个测试模板\"}");

        // Mock service方法
        doNothing().when(cardTemplateService).syncTemplateToRedis(any(JSONObject.class));

        // 执行请求
        mockMvc.perform(post("/template/manual/sync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson.toJSONString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"));

        // 注意：实际的日志验证需要通过日志框架的测试工具来验证
        // 这里主要验证接口调用成功，日志会在控制台输出
    }

    /**
     * 测试获取所有模板接口的日志记录
     * 验证无参数方法的日志记录
     */
    @Test
    void testGetAllTemplatesLogging() throws Exception {
        // 准备测试数据
        Map<String, JSONObject> mockTemplates = new HashMap<>();
        JSONObject template1 = new JSONObject();
        template1.put("title", "模板1");
        template1.put("description", "描述1");
        mockTemplates.put("TEMPLATE_001", template1);

        // Mock service方法
        when(cardTemplateService.getAllTemplates()).thenReturn(mockTemplates);

        // 执行请求
        mockMvc.perform(get("/template/list")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isMap());
    }

    /**
     * 测试删除模板接口的日志记录
     * 验证路径参数的日志记录
     */
    @Test
    void testDeleteTemplateLogging() throws Exception {
        String templateCode = "TEST_TEMPLATE_001";

        // Mock service方法
        when(cardTemplateService.deleteTemplate(anyString())).thenReturn(true);

        // 执行请求
        mockMvc.perform(get("/template/delete/" + templateCode)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true));
    }

    /**
     * 测试异常情况的日志记录
     * 验证异常信息是否正确记录
     */
    @Test
    void testExceptionLogging() throws Exception {
        // Mock service方法抛出异常
        when(cardTemplateService.getAllTemplates()).thenThrow(new RuntimeException("测试异常"));

        // 执行请求，期望返回500错误
        mockMvc.perform(get("/template/list")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }
}
