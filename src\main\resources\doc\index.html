<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta content="width=device-width, initial-scale=1.0"name="viewport"><meta content="smart-doc"name="generator"><title>generative-card-service</title><link href="font.css"rel="stylesheet"><link href="AllInOne.css?v=1756954436419"rel="stylesheet"/><link href="xt256.min.css"rel="stylesheet"><style>.literalblock pre,.listingblock pre:not(.highlight),.listingblock pre[class="highlight"],.listingblock pre[class^="highlight "],.listingblock pre.CodeRay,.listingblock pre.prettyprint{background:#000}.hljs{padding:0}</style><script src="highlight.min.js"></script><script src="jquery.min.js"></script></head><body class="book toc2 toc-left"><div id="header"><h1>generative-card-service</h1><div class="toc2"id="toc"><div id="book-search-input"><input id="search"placeholder="Type to search"type="text"></div><div id="toctitle"><span>API Reference</span></div><ul class="sectlevel1"id="accordion"><li class="open"><a class="dd"href="#_1_生成式卡片接口">1.生成式卡片接口</a><ul class="sectlevel1"><li class="open"><a class="dd"href="#GenerativeCardController">1.1.生成式卡片控制器</a><ul class="sectlevel2"style="display: block"><li><a href="#9d084715f9a7f8b9dd6e167b5ad87a2a">1.1.1.生成式卡片接口</a></li></ul></li></ul></li><li><a class="dd"href="#_2_卡片模板管理接口">2.卡片模板管理接口</a><ul class="sectlevel1"><li class="open"><a class="dd"href="#TemplateController">2.1.模板控制器</a><ul class="sectlevel2"><li><a href="#34c4e7c68f31f0e62c7dd1c3470d6cee">2.1.1.卡片模板同步接口</a></li><li><a href="#015eab74ec3485a6f8cb5001c8cf3660">2.1.2.查询所有模板信息接口</a></li><li><a href="#c2e1a431f52607ef58471ed7220b1642">2.1.3.删除指定模板接口</a></li></ul></li></ul></li></ul></div></div><div id="content"><div id="preamble"><div class="sectionbody"><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Version</th><th class="tableblock halign-left valign-top">Update Time</th><th class="tableblock halign-left valign-top">Status</th><th class="tableblock halign-left valign-top">Author</th><th class="tableblock halign-left valign-top">Description</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">2024-01-01 10:00:00</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">创建</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">AI Lab</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">初始版本，包含生成式卡片和模板管理接口</p></td></tr></tbody></table></div></div><h1 id="_1_生成式卡片接口"><a class="anchor"href="#_1_生成式卡片接口"></a><a class="link"href="#_1_生成式卡片接口">1.生成式卡片接口</a></h1><div class="sect1"><h2 id="GenerativeCardController"><a class="anchor"href="#GenerativeCardController"></a><a class="link"href="#GenerativeCardController">1.1.生成式卡片控制器</a></h2><div class="sectionbody"><div class="sect2"id="9d084715f9a7f8b9dd6e167b5ad87a2a"><h3 id="_1_1_1_生成式卡片接口"><a class="anchor"href="#9d084715f9a7f8b9dd6e167b5ad87a2a"></a><a class="link"href="#9d084715f9a7f8b9dd6e167b5ad87a2a">1.1.1.生成式卡片接口</a></h3><div class="paragraph"data-download="false"data-page=""data-url="http://[域名]/[后缀]/card/stream/generate"id="9d084715f9a7f8b9dd6e167b5ad87a2a-url"><p><strong>URL:</strong><a class="bare"href="http://[域名]/[后缀]/card/stream/generate">&nbsp;http://[域名]/[后缀]/card/stream/generate</a></p></div><div class="paragraph"data-method="POST"id="9d084715f9a7f8b9dd6e167b5ad87a2a-method"><p><strong>Type:&nbsp;</strong>POST</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>AI Lab GW00295473</p></div><div class="paragraph"data-content-type="application/json"id="9d084715f9a7f8b9dd6e167b5ad87a2a-content-type"><p><strong>Content-Type:&nbsp;</strong>application/json</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>生成式卡片接口</p></div><div class="paragraph"><p><strong>Request-headers:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Header</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">Content-Type</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">请求内容类型</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Query-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">version</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">API版本号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X POST -H 'Content-Type: application/json' -H 'Content-Type:application/json' -i 'http://[域名]/[后缀]/card/stream/generate?version=v1' --data '{}'</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">message</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─timeout</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─handler</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─early_send_attempts</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─complete</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─failure</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─send_failed</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─timeout_callback</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─error_callback</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─completion_callback</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">timestamp</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应时间戳</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 200,
  "message": "Success",
  "data": {
    "timeout": 0,
    "handler": {},
    "early_send_attempts": [
      {
        "object": "any object"
      }
    ],
    "complete": true,
    "failure": {},
    "send_failed": true,
    "timeout_callback": {},
    "error_callback": {},
    "completion_callback": {}
  },
  "timestamp": "2024-01-01T10:00:00"
}</code></pre></div></div></div></div></div><h1 id="_2_卡片模板管理接口"><a class="anchor"href="#_2_卡片模板管理接口"></a><a class="link"href="#_2_卡片模板管理接口">2.卡片模板管理接口</a></h1><div class="sect1"><h2 id="TemplateController"><a class="anchor"href="#TemplateController"></a><a class="link"href="#TemplateController">2.1.模板控制器</a></h2><div class="sectionbody"><div class="sect2"id="34c4e7c68f31f0e62c7dd1c3470d6cee"><h3 id="_2_1_1_卡片模板同步接口"><a class="anchor"href="#34c4e7c68f31f0e62c7dd1c3470d6cee"></a><a class="link"href="#34c4e7c68f31f0e62c7dd1c3470d6cee">2.1.1.卡片模板同步接口</a></h3><div class="paragraph"data-download="false"data-page=""data-url="http://[域名]/[后缀]/template/manual/sync"id="34c4e7c68f31f0e62c7dd1c3470d6cee-url"><p><strong>URL:</strong><a class="bare"href="http://[域名]/[后缀]/template/manual/sync">&nbsp;http://[域名]/[后缀]/template/manual/sync</a></p></div><div class="paragraph"data-method="POST"id="34c4e7c68f31f0e62c7dd1c3470d6cee-method"><p><strong>Type:&nbsp;</strong>POST</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>AI Lab GW00295473</p></div><div class="paragraph"data-content-type="application/json"id="34c4e7c68f31f0e62c7dd1c3470d6cee-content-type"><p><strong>Content-Type:&nbsp;</strong>application/json</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>卡片模板同步接口</p></div><div class="paragraph"><p><strong>Request-headers:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Header</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">Content-Type</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">请求内容类型</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Query-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">version</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">API版本号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X POST -H 'Content-Type: application/json' -H 'Content-Type:application/json' -i 'http://[域名]/[后缀]/template/manual/sync?version=v1' --data '{}'</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">message</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">timestamp</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应时间戳</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 200,
  "message": "Success",
  "data": true,
  "timestamp": "2024-01-01T10:00:00"
}</code></pre></div></div></div><div class="sect2"id="015eab74ec3485a6f8cb5001c8cf3660"><h3 id="_2_1_2_查询所有模板信息接口"><a class="anchor"href="#015eab74ec3485a6f8cb5001c8cf3660"></a><a class="link"href="#015eab74ec3485a6f8cb5001c8cf3660">2.1.2.查询所有模板信息接口</a></h3><div class="paragraph"data-download="false"data-page=""data-url="http://[域名]/[后缀]/template/list"id="015eab74ec3485a6f8cb5001c8cf3660-url"><p><strong>URL:</strong><a class="bare"href="http://[域名]/[后缀]/template/list">&nbsp;http://[域名]/[后缀]/template/list</a></p></div><div class="paragraph"data-method="GET"id="015eab74ec3485a6f8cb5001c8cf3660-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>AI Lab</p></div><div class="paragraph"data-content-type="application/x-www-form-urlencoded;charset=UTF-8"id="015eab74ec3485a6f8cb5001c8cf3660-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>查询所有模板信息接口</p></div><div class="paragraph"><p><strong>Request-headers:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Header</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">Content-Type</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">请求内容类型</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Query-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">version</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">API版本号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -H 'Content-Type:application/json' -i 'http://[域名]/[后缀]/template/list?version=v1'</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">message</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─mapKey</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">A map key.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">timestamp</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应时间戳</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 200,
  "message": "Success",
  "data": {
    "mapKey": {}
  },
  "timestamp": "2024-01-01T10:00:00"
}</code></pre></div></div></div><div class="sect2"id="c2e1a431f52607ef58471ed7220b1642"><h3 id="_2_1_3_删除指定模板接口"><a class="anchor"href="#c2e1a431f52607ef58471ed7220b1642"></a><a class="link"href="#c2e1a431f52607ef58471ed7220b1642">2.1.3.删除指定模板接口</a></h3><div class="paragraph"data-download="false"data-page=""data-url="http://[域名]/[后缀]/template/delete/{templateCode}"id="c2e1a431f52607ef58471ed7220b1642-url"><p><strong>URL:</strong><a class="bare"href="http://[域名]/[后缀]/template/delete/{templateCode}">&nbsp;http://[域名]/[后缀]/template/delete/{templateCode}</a></p></div><div class="paragraph"data-method="GET"id="c2e1a431f52607ef58471ed7220b1642-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>AI Lab</p></div><div class="paragraph"data-content-type="application/x-www-form-urlencoded;charset=UTF-8"id="c2e1a431f52607ef58471ed7220b1642-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>删除指定模板接口</p></div><div class="paragraph"><p><strong>Request-headers:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Header</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">Content-Type</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">请求内容类型</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">templateCode</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">模板编码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Query-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">version</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">API版本号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -H 'Content-Type:application/json' -i 'http://[域名]/[后缀]/template/delete/{templateCode}?version=v1'</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">message</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">timestamp</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应时间戳</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 200,
  "message": "Success",
  "data": true,
  "timestamp": "2024-01-01T10:00:00"
}</code></pre></div></div></div></div></div><footer class="page-footer"><span class="copyright">Generated by smart-doc at 2025-09-04 10:53:56</span><span class="footer-modification">Suggestions,contact,support and error reporting on<a href="https://gitee.com/smart-doc-team/smart-doc"target="_blank">&nbsp;Gitee&nbsp;</a>or<a href="https://github.com/smart-doc-group/smart-doc.git"target="_blank">&nbsp;Github</a></span></footer><div href="javascript:void(0)"id="toTop"><img id="upArrow"src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABlUlEQVRIS+2UvUvDQBiH398Rly4udnARwUXs4qAIOigI4iL30dTZ2T9AcNPVvUsXF7uYttdScNDFRRAnB11cFFwKxcXBJTQnJ6lEbRI/CIiY9e6e5/e+9+ZAGX/ImE9/QKCU2jfGbGTQqq4xZgtSyisiKmQgIAAVCCFWAGxnIOhqrdd/xyUrpRZsP40xSwA6AI57vd5eq9W6T6s8tQIppSKi+gDQNREprfVNkiRRwDlfY4xZ+FAIuSOi8Qjw0nEc5XnebZwkViClXA2T5+xhY8xus9ncEUJMAziITN5FEARuXLsGCoQQywBs8uEovJ+Scz7FGDuMSM4cx3E9z+u8r+SDQEq5SEQ1IhoZBE+QnBKRq7V+iEreCDjn84wxCx9NgidITnK5nFutVh/7e14FSqnZIAhqAMY+A4+TADjyfb/Ubref7J4XQXhxNvnEV+AJlbTy+XypUqn4KBaLBZuciCa/A0+opN5oNFz7FpUBbP4EHicxxsyAcz7HGDvvz3nar5+2Ho5wOQwsU5+KNGDa+r8grUP0DBLjtRtNKEliAAAAAElFTkSuQmCC"><span id="upText">Top</span></div></div><script src="search.js?v=1756954436419"></script><script>$(function(){const Accordion=function(el,multiple){this.el=el||{};this.multiple=multiple||false;const links=this.el.find(".dd");links.on("click",{el:this.el,multiple:this.multiple},this.dropdown)};Accordion.prototype.dropdown=function(e){const $el=e.data.el;const $this=$(this),$next=$this.next();$next.slideToggle();$this.parent().toggleClass("open");if(!e.data.multiple){$el.find(".submenu").not($next).slideUp("20").parent().removeClass("open")}};new Accordion($("#accordion"),false);hljs.highlightAll();$(window).scroll(function(){if($(window).scrollTop()>100){let $toTop=$("#toTop");$toTop.fadeIn(1500);$toTop.hover(function(){$("#upArrow").hide();$("#upText").show()},function(){$("#upArrow").show();$("#upText").hide()})}else{$("#toTop").fadeOut(1500)}});$("#toTop").click(function(){$("body, html").animate({scrollTop:0},1000);return false})});</script></body></html>