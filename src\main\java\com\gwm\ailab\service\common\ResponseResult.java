package com.gwm.ailab.service.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 统一响应结果类
 *
 * 该类用于封装所有API接口的响应结果，提供统一的响应格式。
 * 包含响应码、消息、数据和时间戳等信息，便于前端统一处理。
 *
 * @param <T> 响应数据的泛型类型
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResult<T> {

    /**
     * 响应码
     *
     * 标准HTTP状态码：
     * - 200: 成功
     * - 400: 请求参数错误
     * - 401: 未授权
     * - 403: 禁止访问
     * - 404: 资源不存在
     * - 500: 服务器内部错误
     */
    private int code;

    /**
     * 响应消息
     *
     * 描述本次请求的处理结果，成功时通常为"Success"，
     * 失败时包含具体的错误信息，便于调试和用户理解。
     */
    private String message;

    /**
     * 响应数据
     *
     * 实际的业务数据，类型由泛型T决定。
     * 成功时包含请求的数据，失败时通常为null。
     */
    private T data;

    /**
     * 响应时间戳
     *
     * 服务器处理请求的时间，格式为ISO 8601标准时间格式。
     * 用于记录响应生成的准确时间，便于日志分析和调试。
     */
    private LocalDateTime timestamp;

    /**
     * 成功响应
     */
    public static <T> ResponseResult<T> success(T data) {
        return new ResponseResult<>(200, "Success", data, LocalDateTime.now());
    }

    /**
     * 成功响应（无数据）
     */
    public static ResponseResult<Void> success() {
        return new ResponseResult<>(200, "Success", null, LocalDateTime.now());
    }

    /**
     * 成功响应（自定义消息）
     */
    public static <T> ResponseResult<T> success(String message, T data) {
        return new ResponseResult<>(200, message, data, LocalDateTime.now());
    }

    /**
     * 失败响应
     */
    public static <T> ResponseResult<T> error(int code, String message) {
        return new ResponseResult<>(code, message, null, LocalDateTime.now());
    }

    /**
     * 失败响应（默认500错误码）
     */
    public static ResponseResult<Void> error(String message) {
        return new ResponseResult<>(500, message, null, LocalDateTime.now());
    }

    /**
     * 失败响应（默认500错误码）
     */
    public static <T> ResponseResult<T> error(T data, String message) {
        return new ResponseResult<>(500, message, data, LocalDateTime.now());
    }
}
