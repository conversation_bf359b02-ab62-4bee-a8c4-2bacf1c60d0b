spring:
  application:
    name: generative-card-service
  profiles:
    active: qa
  cloud:
    nacos:
      discovery:
        server-addr: nacos-headless:8848
  # <PERSON>配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /generative-card

# 日志配置
logging:
  level:
    root: warn
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
