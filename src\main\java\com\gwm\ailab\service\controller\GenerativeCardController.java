package com.gwm.ailab.service.controller;

import com.alibaba.fastjson2.JSONObject;
import com.gwm.ailab.service.util.OkHttpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;

/**
 * 生成式卡片控制器
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/card")
@Validated
@Tag(name = "服务卡片相关的API接口", description = "服务卡片相关的API接口")
@RequiredArgsConstructor
public class GenerativeCardController {

    private final OkHttpUtil okHttpUtil;

    /**
     * 生成式卡片接口
     *
     * @return SseEmitter 服务端推送事件流
     * <AUTHOR> Lab GW00295473
     * @since 1.0.0
     */
    @PostMapping(value = "/stream/generate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(
            summary = "生成式卡片生成接口",
            description = "通过EventStream流式返回生成服务卡片内容"
    )
    @ApiResponse(
            responseCode = "200",
            description = "成功建立SSE连接，开始流式返回生成结果。",
            content = @Content(mediaType = "text/event-stream")
    )
    public SseEmitter generate(@RequestBody JSONObject json) {
        log.info("开始生成服务卡片，请求参数: {}", json.toJSONString());

        // 创建SSE发射器，设置超时时间为5分钟
        SseEmitter sseEmitter = new SseEmitter(5 * 60 * 1000L);

        try {
            // 构建远程服务URL
            String remoteUrl = "http://generative-card-agent:8000/generative-card-agent/stream/llm_card";

            // 添加必要的请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "text/event-stream");

            // 通过EventStream调用远程服务
            okHttpUtil.eventStream(remoteUrl, json, sseEmitter, headers);

            log.warn("EventStream连接已建立，开始接收数据流");

        } catch (Exception e) {
            log.error("生成服务卡片失败", e);
            sseEmitter.completeWithError(e);
        }
        return sseEmitter;
    }


}
